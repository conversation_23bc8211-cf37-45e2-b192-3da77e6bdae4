/// <reference types="vitest" />
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig(() => {
  const plugins = [react(), tsconfigPaths()];
  const resolve = {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@assets": path.resolve(__dirname, "src/assets"),
      "@components": path.resolve(__dirname, "src/components"),
      "@constants": path.resolve(__dirname, "src/constants"),
      "@enums": path.resolve(__dirname, "src/enums"),
      "@pages": path.resolve(__dirname, "src/pages"),
      "@i18n": path.resolve(__dirname, "src/i18n"),
      "@schemas": path.resolve(__dirname, "src/schemas"),
      "@stores": path.resolve(__dirname, "src/stores"),
      "@utils": path.resolve(__dirname, "src/utils"),
      "@helpers": path.resolve(__dirname, "src/helpers"),
      "@icons": path.resolve(__dirname, "src/icons"),
      "@middlewares": path.resolve(__dirname, "src/middlewares"),
    },
  };

  const test = {
    globals: true,
    environment: "jsdom",
    setupFiles: "./vitest.setup.ts",
    coverage: {
      reporter: ["text", "html", "lcov", "json-summary", "json"],
      include: ["src/**/*.{ts,tsx}"],
      enabled: true,
      exclude: [
        ".cache",
        ".scannerwork",
        ".vscode",
        ".github",
        ".husky",
        "dist",
        "coverage",
        "public",
        "node_modules",
        "**/*.d.ts",
      ],
    },
  };

  return {
    plugins,
    resolve,
    test,
  };
});
