{
  "compilerOptions": {
    /* Base URL */
    "baseUrl": ".",
    "paths": {
      "@assets/*": ["src/assets/*"],
      "@components/*": ["src/components/*"],
      "@constants/*": ["src/constants/*"],
      "@enums/*": ["src/enums/*"],
      "@pages/*": ["src/pages/*"],
      "@i18n/*": ["src/i18n/*"],
      "@schemas/*": ["src/schemas/*"],
      "@stores/*": ["src/stores/*"],
      "@utils/*": ["src/utils/*"],
      "@helpers/*": ["src/helpers/*"],
      "@icons/*": ["src/icons/*"],
      "@middlewares/*": ["src/middlewares/*"]
    },

    /* Core Configuration */
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "bundler",
    "moduleDetection": "force",

    /* React & JSX */
    "jsx": "react-jsx",
    "useDefineForClassFields": true,

    /* Module Compatibility */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "verbatimModuleSyntax": true,

    /* Build & Performance */
    "noEmit": true,
    "skipLibCheck": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.tsbuildinfo",

    /* Type Checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,

    /* Vite Integration */
    "types": [
      "vite/client",
      "vitest",
      "@testing-library/jest-dom",
      "@testing-library/react"
    ]
  },
  "include": ["src/**/*", "vite.config.ts", "vite-env.d.ts"],
  "exclude": [".tanstack", "node_modules", "dist"]
}
