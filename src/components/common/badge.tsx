import { cn } from "@utils/cn";
import type { ReactNode } from "react";

interface BadgeConfig {
  label?: string;
  icon?: string | ReactNode;
  iconAlt?: string;
  containerClasses?: string;
  textClasses?: string;
}

interface BadgeProps<T extends string> {
  type?: T;
  className?: string;
  config: Record<T, BadgeConfig>;
}

const BASE_BADGE_CLASSES = "badge badge-soft badge-primary";
const BASE_TEXT_CLASSES = "font-semibold text-sm";

export const Badge = <T extends string>({ type, className, config }: BadgeProps<T>) => {
  if (!type) {
    return null;
  }

  const badgeConfig = config[type];

  const renderIcon = () => {
    if (!badgeConfig.icon) {
      return null;
    }

    if (typeof badgeConfig.icon === "string") {
      return <img src={badgeConfig.icon} alt={badgeConfig.iconAlt || ""} className="size-4" />;
    }
    return <div>{badgeConfig.icon}</div>;
  };

  return (
    <div className={cn(BASE_BADGE_CLASSES, badgeConfig.containerClasses, className)}>
      {renderIcon()}
      {badgeConfig.label && (
        <span className={cn(BASE_TEXT_CLASSES, badgeConfig.textClasses)}>{badgeConfig.label}</span>
      )}
    </div>
  );
};

export type { BadgeConfig };
