import { Badge, type BadgeConfig } from "@components/common";

export type FollowUpStatusType = "pending" | "contacted" | "interested" | "not_interested";

interface FollowUpBadgeConfig extends BadgeConfig {
  type: FollowUpStatusType;
}

const FOLLOW_UP_BADGE_CONFIG: Record<FollowUpStatusType, FollowUpBadgeConfig> = {
  contacted: {
    containerClasses: "border-primary bg-success-content",
    label: "Contacted",
    textClasses: "text-primary",
    type: "contacted",
  },
  interested: {
    containerClasses: "border-secondary bg-secondary-content/30",
    label: "Interested",
    textClasses: "text-error-content",
    type: "interested",
  },
  not_interested: {
    containerClasses: "border-neutral-content bg-neutral-content/30",
    label: "Not Interested",
    textClasses: "text-neutral-content",
    type: "not_interested",
  },
  pending: {
    containerClasses: "border-warning-content bg-accent/30",
    label: "Pending",
    textClasses: "text-warning-content",
    type: "pending",
  },
};

interface FollowUpStatusBadgeProps {
  type?: FollowUpStatusType;
  className?: string;
}

export const FollowUpBadge = ({ type, className }: FollowUpStatusBadgeProps) => {
  return <Badge type={type} className={className} config={FOLLOW_UP_BADGE_CONFIG} />;
};

export const _FOLLOW_UP_STATUS_OPTIONS = (
  ["pending", "contacted", "interested", "not_interested"] as FollowUpStatusType[]
).map((type) => ({
  label: <FollowUpBadge type={type} />,
  value: type,
}));
